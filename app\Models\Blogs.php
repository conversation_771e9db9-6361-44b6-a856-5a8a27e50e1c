<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Blogs extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'title',
        'type',
        'slug',
        'tags',
        'short_description',
        'description',
        'status',
        'popular',
        'cover_image',
        'banner_image',
        'author_name',
        'author_image',
        'seo',
        'created_at',
        'updated_at'
    ];

    public function category()
    {
        return $this->belongsTo(BlogCategories::class, 'category_id');
    }
   
}