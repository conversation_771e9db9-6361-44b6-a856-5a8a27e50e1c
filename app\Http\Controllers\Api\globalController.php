<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Businesses;
use App\Models\Contacts;
use App\Models\Newsletters;

class globalController extends Controller
{

    public function galleryView()
    {
        $business = Businesses::first();
        // Retrieve and decode the existing media field
        $media = $business->media;
        $imageUrls = json_decode($media, true) ?: [];

        return response()->json($imageUrls);
    }

    public function uploader(request $request)
    {
        $business = Businesses::first();

        if (!$business) {
            return response()->json(['error' => 'Business not found.'], 404);
        }
        $validator = Validator::make($request->all(), [
            'file' => 'required|image|mimes:jpeg,png,jpg,webp|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Handle image upload
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $tinyImagePath = media($file, 'storage/web');
            $tinyImagePath = basename($tinyImagePath);

            $url = asset('storage/web/' . $tinyImagePath);


            // Retrieve and decode the existing media field
            $media = $business->media;
            $mediaArray = json_decode($media, true) ?: [];

            // Append the new image URL
            $mediaArray[] = $url;

            // Encode the array back to JSON and save it
            $business->media = json_encode($mediaArray);
            $business->save();

            // Return the URL in JSON format
            return response()->json(['location' => $url]);
        }

        return response()->json(['error' => 'No file uploaded.'], 400);
    }

    public function webFileUploader(request $request)
    {
        $business = Businesses::first();

        if (!$business) {
            return response()->json(['error' => 'Business not found.'], 404);
        }

        $validator = Validator::make($request->all(), [
            'web_images' => 'required|image|mimes:jpeg,png,jpg,webp|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Handle image upload
        if ($request->hasFile('web_images')) {
            $file = $request->file('web_images');
            $webImagePath = media($file, 'storage/web');
            $webImagePath = basename($webImagePath);

            $url = asset('storage/web/' . $webImagePath);

            // Retrieve and decode the existing media field
            $media = $business->media;
            $mediaArray = json_decode($media, true) ?: [];

            // Append the new image URL
            $mediaArray[] = $url;

            // Encode the array back to JSON and save it
            $business->media = json_encode($mediaArray);
            $business->save();

            // Return the URL in JSON format
            return response()->json(['location' => $url]);
        }

        return response()->json(['error' => 'No file uploaded.'], 400);
    }

    public function deleteFile(request $request)
    {

        $business = Businesses::first();

        if (!$business) {
            return response()->json(['error' => 'Business not found.'], 404);
        }
        $imageToDelete = $request->input('image');

        // Decode the media JSON field
        $mediaArray = json_decode($business->media, true) ?: [];

        // Check if the image exists in the array
        if (!in_array($imageToDelete, $mediaArray)) {
            return response()->json(['message' => 'Image not found in records.'], 404);
        }

        // Remove the image from the array
        $updatedMediaArray = array_filter($mediaArray, function ($image) use ($imageToDelete) {
            return $image !== $imageToDelete;
        });


        // Extract the file path from the full URL
        $imagePath = parse_url($imageToDelete, PHP_URL_PATH); // This will give you "/storage/web/filename.webp"

        // Remove the "/storage/" part and get the relative path inside "web" directory
        $fileName = str_replace('/storage/', '', $imagePath); // This will give "web/filename.webp"

        $publicPath = 'storage/' . $fileName; // For "public/storage/web/"

        // Check if the file exists in "public/storage/web/"
        if (file_exists(public_path($publicPath))) {
            unlink(public_path($publicPath)); // Delete from "public/storage/web/"
        }

        // Save the updated media back to the database
        $business->media = json_encode(array_values($updatedMediaArray)); // Re-index the array
        $business->save();

        return response()->json(['success' => true, 'message' => 'Image deleted successfully.']);
    }

    public function webSettings(request $request)
    {
        $business = Businesses::first();

        if (!$business) {
            return response()->json(['error' => 'Business not found.'], 404);
        }

        $website_logo = [
            'logo' => $request->website_logo,
            'favicon' => $request->favicon,
        ];


        $business->contact_email = $request->contact_email;
        $business->newsletter_email = $request->newsletter_email;


        $business->business_logo = json_encode($website_logo);

        $business->save();

        return response()->json(["message" => "updated !", "status" => "success"], 200);
    }

    public function contactSubmit(Request $request)
    {
        // Validate the input data
        $validator = Validator::make($request->all(), [
            'name'    => 'required|string|max:255',
            'email'   => 'required|email|max:255',
            'phone'   => 'nullable|string|max:20',
            'message' => 'required|string',
        ]);
        // If validation fails, return errors
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        // Save to database
        Contacts::create([
            'name'    => $request->name,
            'email'   => $request->email,
            'phone'   => $request->phone,
            'message' => $request->message,
            'type' => $request->type,
        ]);

        // Return a success response
        return response()->json(['success' => 'Your message has been sent successfully!']);
    }


    public function newsletterSubmit(Request $request)
    {
        // Validate the input data
        $validator = Validator::make($request->all(), [
            'email'   => 'required|email|max:255',
        ]);
        // If validation fails, return errors
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        // Save to database
        Newsletters::create([
         
            'email'   => $request->email,
          
        ]);

        // Return a success response
        return response()->json(['success' => 'Your message has been sent successfully!']);
    }
}
