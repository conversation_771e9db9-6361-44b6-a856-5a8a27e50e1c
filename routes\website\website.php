<?php
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;
use App\Http\Controllers\website\website;


Route::get('/comming-soon', function () {
    return view('website.comingSoon'); 
});

Route::get('/', [website::class, 'HomeView']);
Route::get('/home', [website::class, 'HomeView']);

Route::get('/about', function () {
    if (View::exists('website.about')) {
        return view('website.about');
    }
    
	    return view('website.404');
 
});

Route::get('/contact', function () {
    return view('website.contact'); 
});
Route::get('/consulting', function () {
    return view('website.consult'); 
});
Route::get('/get-quote', function () {
    return view('website.getQuote'); 
});


Route::get('/blog', [website::class, 'blogView']);


Route::get('/blog/detail/{slug}', [website::class, 'singleBlogView'])->name('blog.detail');

Route::get('/services', function () {
    return view('website.service'); 
});

Route::get('/portfolio', function () {
    return view('website.portfolio'); 
});

Route::get('/Business-setup-and-company-formation-in-saudi-arabia', function () {
    return view('website.BusinessSetup'); 
});
Route::get('/taxation-and-auditing-service', function () {
    return view('website.taxationAndAuditing'); 
});
Route::get('/bookkeeping-and-accounting-service', function () {
    return view('website.bookkeepingAndAccounting'); 
});
// GRO / PRO Services in Saudi Arabia
Route::get('/gro-pro-services', function () {
    return view('website.groProServices');
});

// Business Licenses & Certifications
Route::get('/business-licenses-certifications', function () {
    return view('website.businessLicensesCertifications');
});

// Company Liquidation & Ownership Transfers
Route::get('/company-liquidation-ownership-transfers', function () {
    return view('website.companyLiquidationOwnershipTransfers');
});

// Visa & Immigration Services
Route::get('/visa-immigration-services', function () {
    return view('website.visaImmigrationServices');
});

// Corporate Training Programs
Route::get('/corporate-training-programs', function () {
    return view('website.corporateTrainingPrograms');
});

// IT Solutions for Businesses
Route::get('/it-solutions', function () {
    return view('website.itSolutions');
});

// Digital Marketing Services
Route::get('/digital-marketing-services', function () {
    return view('website.digitalMarketingServices');
});

// Legal Advisory Services
Route::get('/legal-advisory-services', function () {
    return view('website.legalAdvisoryServices');
});

